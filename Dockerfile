FROM nvidia/cuda:11.3.1-cudnn8-devel-ubuntu20.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    libgl1-mesa-dev \
    libgl1-mesa-glx \
    libglew-dev \
    libosmesa6-dev \
    libxrender1 \
    libsm6 \
    libxext6 \
    python3-pip \
    python3-dev \
    python3-setuptools \
    python3-wheel \
    patchelf \
    wget \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Create a working directory
WORKDIR /app

# Install Python dependencies
RUN pip3 install --upgrade pip
RUN pip3 install numpy==1.23.5 cython==0.29.36

# Install MuJoCo
RUN mkdir -p /root/.mujoco \
    && wget https://github.com/deepmind/mujoco/releases/download/2.1.0/mujoco210-linux-x86_64.tar.gz -O mujoco.tar.gz \
    && tar -xf mujoco.tar.gz -C /root/.mujoco \
    && rm mujoco.tar.gz

# Set environment variables for MuJoCo
ENV LD_LIBRARY_PATH=/root/.mujoco/mujoco210/bin:${LD_LIBRARY_PATH}
ENV MUJOCO_PY_MUJOCO_PATH=/root/.mujoco/mujoco210

# Install mujoco-py
RUN pip3 install mujoco-py==********

# Install robosuite
RUN pip3 install robosuite

# Copy the project files
COPY . /app

# Set the entrypoint
CMD ["python3", "src/main.py"]
