# GPU Setup for WSL2

This guide will help you configure your WSL2 environment to use your NVIDIA GPU for accelerated computing.

## Prerequisites

1. Windows 11 or Windows 10 (version 2004 or higher)
2. WSL2 installed
3. NVIDIA GPU with compatible drivers installed on Windows

## Setup Instructions

### 1. Copy the `.wslconfig` file to your Windows home directory

The `.wslconfig` file should be placed in your Windows home directory (typically `C:\Users\<USER>\`).

```
# From Windows Command Prompt or PowerShell
copy .wslconfig %USERPROFILE%\
```

### 2. Restart WSL

```
# From Windows Command Prompt or PowerShell
wsl --shutdown
```

Then restart your WSL terminal.

### 3. Install NVIDIA CUDA drivers and tools in WSL

Run the provided setup script:

```bash
./setup_gpu.sh
```

This script will:
- Install CUDA toolkit
- Install PyTorch with CUDA support
- Install TensorFlow with GPU support
- Verify the installations

### 4. Test GPU Configuration

Run the test script to verify that your GPU is working correctly:

```bash
python test_gpu.py
```

This will run performance tests using both PyTorch and TensorFlow to confirm that your GPU is being utilized.

## Troubleshooting

If you encounter issues:

1. Make sure your Windows NVIDIA drivers are up to date
2. Verify that WSL2 is properly installed: `wsl --status`
3. Check NVIDIA GPU visibility in WSL: `nvidia-smi`
4. Ensure the `.wslconfig` file is properly placed in your Windows home directory

## Additional Configuration

For your robotic ultrasound imaging project, you may need to update your environment configuration to explicitly use the GPU:

1. In your Python code, make sure to specify device placement:
   ```python
   import torch
   device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
   model = model.to(device)
   ```

2. For TensorFlow:
   ```python
   import tensorflow as tf
   gpus = tf.config.list_physical_devices('GPU')
   if gpus:
       tf.config.experimental.set_memory_growth(gpus[0], True)
   ```

3. For stable-baselines3, you can specify the device when creating the model:
   ```python
   from stable_baselines3 import PPO
   model = PPO(policy_type, env, policy_kwargs=policy_kwargs, device='cuda')
   ```
