seed: 3
training: True   # Whether to train a model or not

# Settings for stable-baselines RL algorithm
sb_config:
  total_timesteps: 40.0e+6
  check_pt_interval: 1.0e+6
  num_cpu: 4

# Policy settings
sb_policy:
  type: "MlpPolicy"
  net_arch:
    - pi: [256, 128]    # Custom actor network
      vf: [256, 128]    # Value function network

# Specify environment
robosuite:
  env_id: "Ultrasound"
  robots: "Panda"
  use_camera_obs: False
  use_object_obs: False   # Torso obs
  has_renderer: False
  has_offscreen_renderer: False
  render_camera: null
  control_freq: 500
  horizon: 1000
  camera_names: "agentview"
  camera_heights: 48
  camera_widths: 48
  camera_depths: false
  reward_shaping: true
  controller_configs:
    type: "OSC_POSE"
    input_max: 1
    input_min: -1
    output_max: [0.05, 0.05, 0.05, 0.5, 0.5, 0.5]
    output_min: [-0.05, -0.05, -0.05, -0.5, -0.5, -0.5]
    kp: 300
    damping_ratio: 1
    impedance_mode: "tracking"
    kp_limits: [0, 500]
    kp_input_max: 1
    kp_input_min: 0
    damping_ratio_limits: [0, 2]
    position_limits: null
    orientation_limits: null
    uncouple_pos_ori: True
    control_delta: True
    interpolation: null
    ramp_ratio: 0.2
  early_termination: True
  save_data: False
  deterministic_trajectory: False
  torso_solref_randomization: True
  initial_probe_pos_randomization: True
  use_box_torso: True

# Settings used for file handling and logging (save/load destination etc)
file_handling:
  # Logging with Tensorboard and saving trained models
  tb_log_folder: "ppo_ultrasound_tensorboard"
  tb_log_name: "test"
  save_model_folder: "trained_models"
  save_model_filename: "test"
  # Loading of trained models
  load_model_folder: "trained_rl_models"
  load_model_filename: "tracking"
  load_vecnormalize_filename: "vec_normalize_tracking"
  # Path to model which should be continued to train
  continue_training_model_folder: "trained_models"
  continue_training_model_filename: null       # is null when training a new model


