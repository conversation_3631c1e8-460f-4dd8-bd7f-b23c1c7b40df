#!/usr/bin/env python3
import os
import sys

# Path to the cymj.pyx file
cymj_path = os.path.expanduser("~/conda/lib/python3.12/site-packages/mujoco_py/cymj.pyx")

# Read the file
with open(cymj_path, 'r') as f:
    content = f.read()

# Fix the function declarations
content = content.replace("void c_warning_callback(const char *msg) except * nogil:", 
                         "void c_warning_callback(const char *msg) noexcept nogil:")
content = content.replace("void c_error_callback(const char *msg) except * nogil:", 
                         "void c_error_callback(const char *msg) noexcept nogil:")

# Write the file back
with open(cymj_path, 'w') as f:
    f.write(content)

print("Fixed mujoco_py cymj.pyx file")

# Now let's create a simple simulation script that doesn't use robosuite
with open("simple_sim.py", "w") as f:
    f.write("""
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation

# Create a simple 3D animation of a robotic arm
fig = plt.figure(figsize=(10, 8))
ax = fig.add_subplot(111, projection='3d')

# Robot arm parameters
arm_length = 1.0
forearm_length = 0.8

# Initial joint angles
shoulder_angle = 0
elbow_angle = 0

# Create line objects for the arm segments
shoulder_to_elbow, = ax.plot([0, 0], [0, 0], [0, 0], 'r-', linewidth=3)
elbow_to_hand, = ax.plot([0, 0], [0, 0], [0, 0], 'b-', linewidth=3)

# Set axis limits
ax.set_xlim([-2, 2])
ax.set_ylim([-2, 2])
ax.set_zlim([0, 2])
ax.set_xlabel('X')
ax.set_ylabel('Y')
ax.set_zlabel('Z')
ax.set_title('Robotic Arm Simulation')

# Function to update the arm position
def update(frame):
    # Update joint angles
    shoulder_angle = np.sin(frame * 0.05) * np.pi / 2
    elbow_angle = np.cos(frame * 0.05) * np.pi / 2
    
    # Calculate positions
    elbow_x = arm_length * np.sin(shoulder_angle)
    elbow_y = 0
    elbow_z = arm_length * np.cos(shoulder_angle)
    
    hand_x = elbow_x + forearm_length * np.sin(shoulder_angle) * np.cos(elbow_angle)
    hand_y = forearm_length * np.sin(elbow_angle)
    hand_z = elbow_z + forearm_length * np.cos(shoulder_angle) * np.cos(elbow_angle)
    
    # Update line data
    shoulder_to_elbow.set_data([0, elbow_x], [0, elbow_y])
    shoulder_to_elbow.set_3d_properties([0, elbow_z])
    
    elbow_to_hand.set_data([elbow_x, hand_x], [elbow_y, hand_y])
    elbow_to_hand.set_3d_properties([elbow_z, hand_z])
    
    return shoulder_to_elbow, elbow_to_hand

# Create animation
ani = animation.FuncAnimation(fig, update, frames=100, interval=50, blit=True)

# Save animation as a series of images
for i in range(100):
    update(i)
    plt.savefig(f'robot_sim_frame_{i:03d}.png')
    
print("Simulation complete! Check the robot_sim_frame_*.png files.")
""")

print("Created simple simulation script")
