#!/usr/bin/env python3
import os
import sys

# Path to the cymj.pyx file
cymj_path = os.path.expanduser("~/conda/lib/python3.12/site-packages/mujoco_py/cymj.pyx")

# Read the file
with open(cymj_path, 'r') as f:
    content = f.read()

# Fix the noexcept issue
content = content.replace("void c_warning_callback(const char *msg) except * nogil:", 
                         "void c_warning_callback(const char *msg) noexcept nogil:")
content = content.replace("void c_error_callback(const char *msg) except * nogil:", 
                         "void c_error_callback(const char *msg) noexcept nogil:")

# Write the file back
with open(cymj_path, 'w') as f:
    f.write(content)

print("Fixed mujoco_py cymj.pyx file")
