import cv2
import os
import numpy as np
import glob

# Get all the frame files
frame_files = sorted(glob.glob('robot_sim_frame_*.png'))

if not frame_files:
    print("No frame files found!")
    exit(1)

# Read the first frame to get dimensions
first_frame = cv2.imread(frame_files[0])
height, width, layers = first_frame.shape

# Create video writer
fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # or 'XVID'
video = cv2.VideoWriter('robot_simulation.mp4', fourcc, 20.0, (width, height))

# Add frames to video
for frame_file in frame_files:
    frame = cv2.imread(frame_file)
    video.write(frame)

# Release the video writer
video.release()

print("Video created: robot_simulation.mp4")
