name: lint_python
on: [pull_request, push]
jobs:
  lint_python:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-python@v2
      - run: pip install isort mypy pytest pyupgrade safety
      - run: isort --check-only --profile black . || true
      - run: pip install -e .[nomujoco]
      - run: mypy --install-types --non-interactive . || true
      - run: pytest . || true
      - run: pytest --doctest-modules . || true
      - run: shopt -s globstar && pyupgrade --py36-plus **/*.py || true
