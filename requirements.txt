absl-py==0.11.0
ansitable==0.9.5
astroid==2.4.2
cachetools==4.1.1
certifi==2020.6.20
cffi==1.14.3
chardet==3.0.4
cloudpickle==1.6.0
colored==1.4.2
cycler==0.10.0
Cython==0.29.33
dataclasses==0.6
fasteners==0.15
future==0.18.2
glfw==1.12.0
google-auth==1.23.0
google-auth-oauthlib==0.4.2
grpcio==1.33.2
gym==0.17.3
h5py==3.1.0
idna==2.10
imageio==2.9.0
isort==5.5.4
kiwisolver==1.3.1
Klampt==0.8.6
lazy-object-proxy==1.4.3
llvmlite==0.34.0
Markdown==3.3.3
matplotlib==3.3.2
mccabe==0.6.1
monotonic==1.5
mpmath==1.2.1
numba==0.51.2
numpy==1.20.0
numpy-quaternion==2020.*********.49
oauthlib==3.1.0
pandas==1.1.4
pgraph-python==0.6.1
Pillow==7.2.0
protobuf==3.14.0
pyasn1==0.4.8
pyasn1-modules==0.2.8
pycparser==2.20
pyglet==1.5.0
pylint==2.6.0
pyparsing==2.4.7
python-dateutil==2.8.1
pytz==2020.4
PyYAML==5.4.1
qpsolvers==1.5
quadprog==0.1.8
Quaternion==3.5.2.post4
requests==2.25.0
requests-oauthlib==1.3.0
roboticstoolbox-python==0.9.1
rsa==4.6
# rtb-data==0.9
rtb-data==1.0.1
scipy==1.5.2
seaborn==0.11.0
six==1.15.0
spatialmath-python==0.9.2
swift-sim==0.8.1
sympy==1.7.1
tensorboard==2.4.0
tensorboard-plugin-wit==1.7.0
# toml==0.10.1
# torch==1.8.0+cu111
# torchaudio==0.8.0
# torchvision==0.9.0+cu111
# # torch>=1.8.0
# torchaudio>=0.8.0
# torchvision>=0.9.0
# --find-links https://download.pytorch.org/whl/torch_stable.html
transforms3d==0.3.1
# typing-extensions==*******
# urllib3==1.26.2
# websockets==8.1
# Werkzeug==1.0.1
# wrapt==1.12.1
# numpy==1.20.3
# torch==1.8.2+cu111
# torchaudio==0.8.2
# torchvision==0.9.2+cu111
# --find-links https://download.pytorch.org/whl/lts/1.8/torch_lts.html
# git+https://github.com/DLR-RM/stable-baselines3
# stable-baselines3==1.7.0
git+https://github.com/hermanjakobsen/mujoco-py
git+https://github.com/hermanjakobsen/robosuite